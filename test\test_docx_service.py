"""
@Description : Test DOCX processing service
<AUTHOR> <PERSON>
@Copyright   : 浙江求是半导体设备有限公司
@Date        : 2025-05-30 10:31:14
@LastEditors : <PERSON>
@LastEditTime: 2025-06-10 08:54:42
@Modified    : <PERSON>
"""
import requests
import json
import os


def test_docx_service():
    # 服务地址
    base_url = 'http://localhost:5555'
    process_url = f'{base_url}/process_docx'
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json"
    }
    
    try:
        # 获取当前脚本所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        # 获取项目根目录（test文件夹的上级目录）
        project_root = os.path.dirname(current_dir)
        
        # 读取example.json文件
        print("正在读取example.json文件...")
        example_json_path = os.path.join(current_dir, 'example.json')
        with open(example_json_path, 'r', encoding='utf-8') as f:
            content = f.read()
            try:
                data = json.loads(content)
                print("JSON文件解析成功！")
                
                # 修改模板文件名为docx
                data['template_name'] = 'template.docx'
                
            except json.JSONDecodeError as je:
                print(f"JSON解析错误：{str(je)}")
                print(f"错误位置：行 {je.lineno}, 列 {je.colno}")
                print(f"错误内容：{je.msg}")
                return
            
        # 确保template.docx文件存在 - 使用绝对路径
        template_path = os.path.join(project_root, data['template_name'])
        if not os.path.exists(template_path):
            print(f"错误：模板文件 {template_path} 不存在！")
            print(f"项目根目录：{project_root}")
            return
            
        print(f"找到模板文件：{template_path}")
        
        # 发送POST请求生成DOCX
        print("正在发送请求生成DOCX...")
        response = requests.post(process_url, json=data, headers=headers)
        
        # 检查响应状态
        if response.status_code == 200:
            # 服务器直接返回文件流，不是JSON
            content_type = response.headers.get('content-type', '')
            if 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' in content_type:
                # 获取文件名
                content_disposition = response.headers.get('Content-Disposition', '')
                filename = 'generated.docx'
                if 'filename=' in content_disposition:
                    filename = content_disposition.split('filename=')[1].strip('"')
                
                # 保存文件到当前目录
                output_path = os.path.join(current_dir, filename)
                with open(output_path, 'wb') as f:
                    f.write(response.content)
                
                print("DOCX生成成功！")
                print(f"文件已保存到：{output_path}")
            else:
                print(f"意外的响应类型：{content_type}")
                print(f"响应内容：{response.text[:200]}...")
        else:
            # 打印错误信息
            try:
                error_msg = response.json()
                print(f"错误：{error_msg.get('error', '未知错误')}")
            except json.JSONDecodeError:
                print(f"错误：服务器返回非JSON格式响应：{response.text}")
            print(f"状态码：{response.status_code}")
            
    except FileNotFoundError:
        print("错误：找不到example.json文件！")
        print(f"当前工作目录：{os.getcwd()}")
    except requests.exceptions.ConnectionError:
        print("错误：无法连接到服务器，请确保服务正在运行！")
    except Exception as e:
        print(f"发生错误：{str(e)}")
        print(f"错误类型：{type(e).__name__}")


if __name__ == "__main__":
    test_docx_service()