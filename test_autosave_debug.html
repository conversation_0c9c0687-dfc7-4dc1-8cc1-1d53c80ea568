<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动保存功能调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .debug-panel {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .status-item {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .status-ok { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-error { background: #f8d7da; color: #721c24; }
        button {
            margin: 5px;
            padding: 8px 16px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        #log {
            background: #000;
            color: #0f0;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>自动保存功能调试页面</h1>
    
    <div class="debug-panel">
        <h3>系统状态检查</h3>
        <div id="system-status"></div>
        <button class="btn-primary" onclick="checkSystemStatus()">刷新状态</button>
        <button class="btn-warning" onclick="diagnoseAutoSave()">诊断自动保存</button>
    </div>
    
    <div class="debug-panel">
        <h3>自动保存测试</h3>
        <p>在下面的文本框中输入内容，观察自动保存功能是否正常工作：</p>
        <form id="test-form">
            <textarea name="test_field_1" placeholder="测试字段1 - 在这里输入内容测试自动保存"></textarea>
            <textarea name="test_field_2" placeholder="测试字段2 - 修改内容观察保存状态"></textarea>
        </form>
        
        <div>
            <button class="btn-success" onclick="triggerManualSave()">手动触发保存</button>
            <button class="btn-warning" onclick="recoverContext()">恢复上下文</button>
            <button class="btn-danger" onclick="resetAutoSave()">重置自动保存</button>
        </div>
    </div>
    
    <div class="debug-panel">
        <h3>会话存储测试</h3>
        <button class="btn-primary" onclick="testSessionStorage()">测试SessionStorage</button>
        <button class="btn-primary" onclick="clearSessionStorage()">清除SessionStorage</button>
        <div id="session-storage-status"></div>
    </div>
    
    <div class="debug-panel">
        <h3>实时日志</h3>
        <div id="log"></div>
        <button class="btn-warning" onclick="clearLog()">清除日志</button>
    </div>

    <script>
        // 日志功能
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 系统状态检查
        function checkSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let html = '';
            
            // 检查各个组件
            const components = [
                { name: 'window.apiClient', obj: window.apiClient },
                { name: 'window.conversationManager', obj: window.conversationManager },
                { name: 'window.conversationUI', obj: window.conversationUI },
                { name: 'window.autoSaveManager', obj: window.autoSaveManager },
                { name: 'window.dataManager', obj: window.dataManager }
            ];
            
            components.forEach(comp => {
                const status = comp.obj ? 'status-ok' : 'status-error';
                const text = comp.obj ? '✓ 已加载' : '❌ 未加载';
                html += `<div class="status-item ${status}">${comp.name}: ${text}</div>`;
            });
            
            // 检查表单
            const form = document.getElementById('test-form');
            const formStatus = form ? 'status-ok' : 'status-error';
            const formText = form ? '✓ 存在' : '❌ 不存在';
            html += `<div class="status-item ${formStatus}">测试表单: ${formText}</div>`;
            
            statusDiv.innerHTML = html;
            log('系统状态检查完成');
        }
        
        // 诊断自动保存
        function diagnoseAutoSave() {
            if (window.autoSaveManager && typeof window.autoSaveManager.diagnose === 'function') {
                const status = window.autoSaveManager.diagnose();
                log(`自动保存诊断完成: ${JSON.stringify(status, null, 2)}`);
            } else {
                log('❌ 自动保存管理器不可用或没有诊断方法');
            }
        }
        
        // 手动触发保存
        function triggerManualSave() {
            if (window.autoSaveManager && typeof window.autoSaveManager.saveNow === 'function') {
                window.autoSaveManager.saveNow();
                log('手动触发保存');
            } else {
                log('❌ 自动保存管理器不可用');
            }
        }
        
        // 恢复上下文
        function recoverContext() {
            if (window.autoSaveManager && typeof window.autoSaveManager.checkAndRecoverContext === 'function') {
                window.autoSaveManager.checkAndRecoverContext();
                log('尝试恢复自动保存上下文');
            } else {
                log('❌ 自动保存管理器不可用');
            }
        }
        
        // 重置自动保存
        function resetAutoSave() {
            if (window.autoSaveManager) {
                window.autoSaveManager.contextRestored = false;
                window.autoSaveManager.currentConversationId = null;
                window.autoSaveManager.currentVersionId = null;
                window.autoSaveManager.retryAttempts = 0;
                log('自动保存状态已重置');
            } else {
                log('❌ 自动保存管理器不可用');
            }
        }
        
        // 测试SessionStorage
        function testSessionStorage() {
            const statusDiv = document.getElementById('session-storage-status');
            let html = '';
            
            try {
                // 检查SessionStorage可用性
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                html += '<div class="status-item status-ok">✓ SessionStorage 可用</div>';
                
                // 检查自动保存相关的存储
                const keys = ['autoSave_conversationId', 'autoSave_versionId', 'autoSave_timestamp'];
                keys.forEach(key => {
                    const value = sessionStorage.getItem(key);
                    const status = value ? 'status-ok' : 'status-warning';
                    const text = value ? `✓ ${value}` : '⚠️ 未设置';
                    html += `<div class="status-item ${status}">${key}: ${text}</div>`;
                });
                
                log('SessionStorage 测试完成');
            } catch (e) {
                html += '<div class="status-item status-error">❌ SessionStorage 不可用</div>';
                log(`SessionStorage 测试失败: ${e.message}`);
            }
            
            statusDiv.innerHTML = html;
        }
        
        // 清除SessionStorage
        function clearSessionStorage() {
            try {
                const keys = ['autoSave_conversationId', 'autoSave_versionId', 'autoSave_timestamp'];
                keys.forEach(key => {
                    sessionStorage.removeItem(key);
                });
                log('SessionStorage 已清除');
                testSessionStorage(); // 刷新显示
            } catch (e) {
                log(`清除 SessionStorage 失败: ${e.message}`);
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('调试页面加载完成');
            checkSystemStatus();
            testSessionStorage();
            
            // 监听自动保存事件
            document.addEventListener('autoSaveSuccess', function(e) {
                log(`自动保存成功: ${JSON.stringify(e.detail)}`);
            });
            
            // 监听对话加载事件
            document.addEventListener('conversationLoaded', function(e) {
                log(`对话加载事件: ${JSON.stringify(e.detail)}`);
            });
        });
    </script>
</body>
</html>
