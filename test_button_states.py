#!/usr/bin/env python3
"""
测试按钮状态在版本切换时的正确性
"""

import requests
import json

BASE_URL = 'http://localhost:5555'

def login_and_get_cookies():
    """登录并获取cookies"""
    data = {
        'username': 'testuser',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/auth/login', json=data)
        if response.json().get('success'):
            return response.cookies
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def create_version_with_minimal_data(cookies):
    """创建只有最少数据的版本"""
    form_data = {
        'problem_description': '最小测试问题',
        'product_name': '测试产品',
        # D5只有1个措施
        'd5_measure1': '基本纠正措施',
        'd5_responsible1': '负责人A',
        'd5_date1': '2024-01-01',
        # D6只有1个验证
        'd6_verification1': '基本验证措施',
        'd6_verifier1': '验证人A',
        'd6_verify_time1': '2024-01-02',
        'd6_result1': '验证通过',
        # D7只有1个预防
        'd7_prevention1': '基本预防措施',
        'd7_responsible1': '负责人A',
        'd7_date1': '2024-01-03'
    }
    
    data = {
        'version_id': 'v_minimal_001',
        'version_name': '最小数据版本',
        'form_data': form_data,
        'created_by': 'user'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/test_conv_001/versions', json=data, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"创建最小数据版本失败: {e}")
        return False

def create_version_with_maximal_data(cookies):
    """创建有最大数据的版本"""
    form_data = {
        'problem_description': '最大测试问题',
        'product_name': '测试产品',
        # D5有4个措施（最大值）
        'd5_measure1': '纠正措施1',
        'd5_responsible1': '负责人1',
        'd5_date1': '2024-01-01',
        'd5_measure2': '纠正措施2',
        'd5_responsible2': '负责人2',
        'd5_date2': '2024-01-02',
        'd5_measure3': '纠正措施3',
        'd5_responsible3': '负责人3',
        'd5_date3': '2024-01-03',
        'd5_measure4': '纠正措施4',
        'd5_responsible4': '负责人4',
        'd5_date4': '2024-01-04',
        # D6有4个验证（最大值）
        'd6_verification1': '验证措施1',
        'd6_verifier1': '验证人1',
        'd6_verify_time1': '2024-01-05',
        'd6_result1': '验证结果1',
        'd6_verification2': '验证措施2',
        'd6_verifier2': '验证人2',
        'd6_verify_time2': '2024-01-06',
        'd6_result2': '验证结果2',
        'd6_verification3': '验证措施3',
        'd6_verifier3': '验证人3',
        'd6_verify_time3': '2024-01-07',
        'd6_result3': '验证结果3',
        'd6_verification4': '验证措施4',
        'd6_verifier4': '验证人4',
        'd6_verify_time4': '2024-01-08',
        'd6_result4': '验证结果4',
        # D7有4个预防（最大值）
        'd7_prevention1': '预防措施1',
        'd7_responsible1': '负责人1',
        'd7_date1': '2024-01-09',
        'd7_prevention2': '预防措施2',
        'd7_responsible2': '负责人2',
        'd7_date2': '2024-01-10',
        'd7_prevention3': '预防措施3',
        'd7_responsible3': '负责人3',
        'd7_date3': '2024-01-11',
        'd7_prevention4': '预防措施4',
        'd7_responsible4': '负责人4',
        'd7_date4': '2024-01-12'
    }
    
    data = {
        'version_id': 'v_maximal_001',
        'version_name': '最大数据版本',
        'form_data': form_data,
        'created_by': 'user'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/test_conv_001/versions', json=data, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"创建最大数据版本失败: {e}")
        return False

def set_active_version(cookies, version_id):
    """设置活跃版本"""
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/test_conv_001/versions/{version_id}/activate', cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"设置活跃版本失败: {e}")
        return False

def get_active_version(cookies):
    """获取活跃版本"""
    try:
        response = requests.get(f'{BASE_URL}/api/conversations/test_conv_001/versions/active', cookies=cookies)
        result = response.json()
        if result.get('success'):
            return result.get('version')
        return None
    except Exception as e:
        print(f"获取活跃版本失败: {e}")
        return None

def count_elements_in_data(form_data, prefix):
    """计算数据中特定前缀的元素数量"""
    count = 0
    i = 1
    while f'{prefix}{i}' in form_data:
        count += 1
        i += 1
    return count

def main():
    """主测试函数"""
    print("开始按钮状态测试...")
    
    # 登录
    cookies = login_and_get_cookies()
    if not cookies:
        print("✗ 登录失败")
        return
    print("✓ 登录成功")
    
    # 创建最小数据版本
    if create_version_with_minimal_data(cookies):
        print("✓ 创建最小数据版本成功")
    else:
        print("✗ 创建最小数据版本失败")
        return
    
    # 创建最大数据版本
    if create_version_with_maximal_data(cookies):
        print("✓ 创建最大数据版本成功")
    else:
        print("✗ 创建最大数据版本失败")
        return
    
    print("\n开始测试版本切换和按钮状态...")
    
    # 切换到最小数据版本
    if set_active_version(cookies, 'v_minimal_001'):
        print("✓ 切换到最小数据版本成功")
        active = get_active_version(cookies)
        if active:
            form_data = active['form_data']
            d5_count = count_elements_in_data(form_data, 'd5_measure')
            d6_count = count_elements_in_data(form_data, 'd6_verification')
            d7_count = count_elements_in_data(form_data, 'd7_prevention')
            print(f"  D5措施数量: {d5_count} (应该可以添加更多)")
            print(f"  D6验证数量: {d6_count} (应该可以添加更多)")
            print(f"  D7预防数量: {d7_count} (应该可以添加更多)")
    
    # 切换到最大数据版本
    if set_active_version(cookies, 'v_maximal_001'):
        print("✓ 切换到最大数据版本成功")
        active = get_active_version(cookies)
        if active:
            form_data = active['form_data']
            d5_count = count_elements_in_data(form_data, 'd5_measure')
            d6_count = count_elements_in_data(form_data, 'd6_verification')
            d7_count = count_elements_in_data(form_data, 'd7_prevention')
            print(f"  D5措施数量: {d5_count} (应该达到上限，不能添加)")
            print(f"  D6验证数量: {d6_count} (应该达到上限，不能添加)")
            print(f"  D7预防数量: {d7_count} (应该达到上限，不能添加)")
    
    # 再次切换回最小数据版本
    if set_active_version(cookies, 'v_minimal_001'):
        print("✓ 再次切换到最小数据版本成功")
        active = get_active_version(cookies)
        if active:
            form_data = active['form_data']
            d5_count = count_elements_in_data(form_data, 'd5_measure')
            d6_count = count_elements_in_data(form_data, 'd6_verification')
            d7_count = count_elements_in_data(form_data, 'd7_prevention')
            print(f"  D5措施数量: {d5_count} (应该又可以添加了)")
            print(f"  D6验证数量: {d6_count} (应该又可以添加了)")
            print(f"  D7预防数量: {d7_count} (应该又可以添加了)")
    
    print("\n按钮状态测试完成!")
    print("请在浏览器中验证：")
    print("1. 切换到最小数据版本时，增加按钮应该可用")
    print("2. 切换到最大数据版本时，增加按钮应该被禁用")
    print("3. 计数器显示应该正确反映当前版本的元素数量")

if __name__ == '__main__':
    main()
