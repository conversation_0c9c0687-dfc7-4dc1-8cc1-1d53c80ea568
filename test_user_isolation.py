#!/usr/bin/env python3
"""
测试用户数据隔离
验证不同用户的数据是否完全独立
"""

import requests
import json

BASE_URL = 'http://localhost:5555'

def create_user_and_login(username, email, password):
    """创建用户并登录"""
    # 注册用户
    register_data = {
        'username': username,
        'email': email,
        'password': password,
        'full_name': f'{username}用户'
    }
    
    try:
        # 先尝试注册
        response = requests.post(f'{BASE_URL}/api/auth/register', json=register_data)
        if not response.json().get('success'):
            print(f"用户 {username} 可能已存在，直接尝试登录")
        
        # 登录
        login_data = {
            'username': username,
            'password': password
        }
        
        response = requests.post(f'{BASE_URL}/api/auth/login', json=login_data)
        result = response.json()
        
        if result.get('success'):
            return response.cookies
        else:
            print(f"用户 {username} 登录失败: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"创建/登录用户 {username} 失败: {e}")
        return None

def create_test_data(cookies, user_prefix):
    """为用户创建测试数据"""
    # 创建对话
    conv_data = {
        'conversation_id': f'{user_prefix}_conversation_001',
        'title': f'{user_prefix}的测试对话',
        'description': f'这是{user_prefix}的专属对话'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations', json=conv_data, cookies=cookies)
        if not response.json().get('success'):
            print(f"创建{user_prefix}对话失败")
            return False
        
        # 创建表单记录
        record_data = {
            'record_id': f'{user_prefix}_record_001',
            'title': f'{user_prefix}的测试记录',
            'form_data': {
                'problem_description': f'这是{user_prefix}的问题描述',
                'product_name': f'{user_prefix}的产品',
                'd4_man1': f'{user_prefix}的人员原因',
                'd5_measure1': f'{user_prefix}的纠正措施'
            },
            'record_type': 'draft'
        }
        
        response = requests.post(f'{BASE_URL}/api/records', json=record_data, cookies=cookies)
        if not response.json().get('success'):
            print(f"创建{user_prefix}记录失败")
            return False
        
        # 创建版本
        version_data = {
            'version_id': f'{user_prefix}_version_001',
            'version_name': f'{user_prefix}的版本',
            'form_data': {
                'problem_description': f'这是{user_prefix}的版本问题',
                'product_name': f'{user_prefix}的版本产品'
            },
            'created_by': 'user'
        }
        
        response = requests.post(f'{BASE_URL}/api/conversations/{user_prefix}_conversation_001/versions', 
                               json=version_data, cookies=cookies)
        if not response.json().get('success'):
            print(f"创建{user_prefix}版本失败")
            return False
        
        print(f"✓ {user_prefix}的测试数据创建成功")
        return True
        
    except Exception as e:
        print(f"创建{user_prefix}测试数据失败: {e}")
        return False

def get_user_data(cookies, user_prefix):
    """获取用户数据"""
    try:
        # 获取对话
        response = requests.get(f'{BASE_URL}/api/conversations', cookies=cookies)
        conversations = response.json().get('conversations', []) if response.json().get('success') else []
        
        # 获取记录
        response = requests.get(f'{BASE_URL}/api/records', cookies=cookies)
        records = response.json().get('records', []) if response.json().get('success') else []
        
        # 获取版本
        versions = []
        for conv in conversations:
            response = requests.get(f'{BASE_URL}/api/conversations/{conv["conversation_id"]}/versions', cookies=cookies)
            if response.json().get('success'):
                versions.extend(response.json().get('versions', []))
        
        return {
            'conversations': conversations,
            'records': records,
            'versions': versions
        }
        
    except Exception as e:
        print(f"获取{user_prefix}数据失败: {e}")
        return {'conversations': [], 'records': [], 'versions': []}

def check_data_isolation(user1_data, user2_data, user1_prefix, user2_prefix):
    """检查数据隔离性"""
    print(f"\n=== 数据隔离检查 ===")
    
    # 检查对话隔离
    user1_conv_ids = [conv['conversation_id'] for conv in user1_data['conversations']]
    user2_conv_ids = [conv['conversation_id'] for conv in user2_data['conversations']]
    
    print(f"{user1_prefix}的对话: {user1_conv_ids}")
    print(f"{user2_prefix}的对话: {user2_conv_ids}")
    
    conv_overlap = set(user1_conv_ids) & set(user2_conv_ids)
    if conv_overlap:
        print(f"❌ 对话数据泄露！共享的对话ID: {conv_overlap}")
        return False
    else:
        print("✅ 对话数据完全隔离")
    
    # 检查记录隔离
    user1_record_ids = [record['record_id'] for record in user1_data['records']]
    user2_record_ids = [record['record_id'] for record in user2_data['records']]
    
    print(f"{user1_prefix}的记录: {user1_record_ids}")
    print(f"{user2_prefix}的记录: {user2_record_ids}")
    
    record_overlap = set(user1_record_ids) & set(user2_record_ids)
    if record_overlap:
        print(f"❌ 记录数据泄露！共享的记录ID: {record_overlap}")
        return False
    else:
        print("✅ 记录数据完全隔离")
    
    # 检查版本隔离
    user1_version_ids = [version['version_id'] for version in user1_data['versions']]
    user2_version_ids = [version['version_id'] for version in user2_data['versions']]
    
    print(f"{user1_prefix}的版本: {user1_version_ids}")
    print(f"{user2_prefix}的版本: {user2_version_ids}")
    
    version_overlap = set(user1_version_ids) & set(user2_version_ids)
    if version_overlap:
        print(f"❌ 版本数据泄露！共享的版本ID: {version_overlap}")
        return False
    else:
        print("✅ 版本数据完全隔离")
    
    return True

def main():
    """主测试函数"""
    print("开始用户数据隔离测试...")
    
    # 创建两个测试用户
    user1_cookies = create_user_and_login('testuser1', '<EMAIL>', '123456')
    user2_cookies = create_user_and_login('testuser2', '<EMAIL>', '123456')
    
    if not user1_cookies or not user2_cookies:
        print("❌ 用户创建/登录失败")
        return
    
    print("✅ 两个用户登录成功")
    
    # 为每个用户创建测试数据
    if not create_test_data(user1_cookies, 'USER1'):
        print("❌ 用户1数据创建失败")
        return
    
    if not create_test_data(user2_cookies, 'USER2'):
        print("❌ 用户2数据创建失败")
        return
    
    # 获取用户数据
    user1_data = get_user_data(user1_cookies, 'USER1')
    user2_data = get_user_data(user2_cookies, 'USER2')
    
    print(f"\n用户1数据统计: {len(user1_data['conversations'])}个对话, {len(user1_data['records'])}个记录, {len(user1_data['versions'])}个版本")
    print(f"用户2数据统计: {len(user2_data['conversations'])}个对话, {len(user2_data['records'])}个记录, {len(user2_data['versions'])}个版本")
    
    # 检查数据隔离
    if check_data_isolation(user1_data, user2_data, 'USER1', 'USER2'):
        print("\n🎉 数据隔离测试通过！不同用户的数据完全独立。")
    else:
        print("\n❌ 数据隔离测试失败！存在数据泄露问题。")
    
    print("\n测试完成！现在可以在浏览器中验证：")
    print("1. 使用testuser1登录，应该只看到USER1的数据")
    print("2. 登出后使用testuser2登录，应该只看到USER2的数据")
    print("3. 数据应该完全不同，没有任何重叠")

if __name__ == '__main__':
    main()
