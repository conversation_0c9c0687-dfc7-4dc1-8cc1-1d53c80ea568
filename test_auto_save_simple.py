#!/usr/bin/env python3
"""
简单的自动保存测试
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5555'

def main():
    # 登录
    login_data = {'username': 'testuser', 'password': '123456'}
    response = requests.post(f'{BASE_URL}/api/auth/login', json=login_data)
    if not response.json().get('success'):
        print("登录失败")
        return
    
    cookies = response.cookies
    print("✓ 登录成功")
    
    # 创建对话
    timestamp = int(time.time())
    conv_id = f'test_conv_{timestamp}'
    conv_data = {
        'conversation_id': conv_id,
        'title': f'测试对话_{timestamp}',
        'description': '自动保存测试'
    }
    
    response = requests.post(f'{BASE_URL}/api/conversations', json=conv_data, cookies=cookies)
    if not response.json().get('success'):
        print("创建对话失败")
        return
    
    print("✓ 创建对话成功")
    
    # 创建版本
    version_id = f'v_{timestamp}'
    version_data = {
        'version_id': version_id,
        'version_name': '测试版本',
        'form_data': {
            'problem_description': '初始问题',
            'product_name': '测试产品'
        },
        'created_by': 'user'
    }
    
    response = requests.post(f'{BASE_URL}/api/conversations/{conv_id}/versions', 
                           json=version_data, cookies=cookies)
    if not response.json().get('success'):
        print("创建版本失败")
        return
    
    print("✓ 创建版本成功")
    
    # 测试更新
    update_data = {
        'form_data': {
            'problem_description': '更新后的问题描述',
            'product_name': '更新后的产品',
            'd4_man1': '新增的人员原因',
            'd5_measure1': '新增的纠正措施'
        }
    }
    
    response = requests.put(f'{BASE_URL}/api/conversations/{conv_id}/versions/{version_id}',
                          json=update_data, cookies=cookies)
    
    if response.json().get('success'):
        print("✓ 版本更新成功")
        
        # 验证数据
        response = requests.get(f'{BASE_URL}/api/conversations/{conv_id}/versions/active', 
                              cookies=cookies)
        if response.json().get('success'):
            version = response.json().get('version')
            form_data = version.get('form_data', {})
            
            print(f"✓ 数据验证成功")
            print(f"  问题描述: {form_data.get('problem_description')}")
            print(f"  产品名称: {form_data.get('product_name')}")
            print(f"  人员原因: {form_data.get('d4_man1')}")
            print(f"  纠正措施: {form_data.get('d5_measure1')}")
            
            print("\n🎉 自动保存功能测试成功！")
            print("现在可以在浏览器中测试前端自动保存功能")
        else:
            print("❌ 数据验证失败")
    else:
        print("❌ 版本更新失败")

if __name__ == '__main__':
    main()
