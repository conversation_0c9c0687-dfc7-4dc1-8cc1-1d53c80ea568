/**
 * 数据管理器 - 替换localStorage的数据存储逻辑
 * 提供与后端数据库交互的统一接口
 */

class DataManager {
    constructor() {
        this.apiClient = window.apiClient;
        this.currentRecordId = null;
        this.isInitialized = false;
    }

    /**
     * 初始化数据管理器
     */
    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // 确保用户已登录
            const user = await this.apiClient.getCurrentUser();
            if (!user) {
                throw new Error('用户未登录');
            }
            
            this.isInitialized = true;
            console.log('数据管理器初始化成功');
        } catch (error) {
            console.error('数据管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 生成唯一ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    /**
     * 检查表单是否为空
     */
    isFormEmpty(formData) {
        if (!formData || typeof formData !== 'object') return true;
        
        for (const key in formData) {
            if (formData[key] && String(formData[key]).trim() !== '') {
                return false;
            }
        }
        return true;
    }

    /**
     * 生成记录标题
     */
    generateRecordTitle(formData) {
        // 优先使用问题描述
        if (formData.problem_description && formData.problem_description.trim()) {
            const desc = formData.problem_description.trim();
            return desc.length > 30 ? desc.substring(0, 30) + '...' : desc;
        }
        
        // 其次使用产品名称
        if (formData.product_name && formData.product_name.trim()) {
            return `${formData.product_name.trim()} - 8D报告`;
        }
        
        // 最后使用时间戳
        return `8D报告 - ${new Date().toLocaleString()}`;
    }

    // ==================== 表单记录管理 ====================

    /**
     * 获取所有记录
     */
    async getAllRecords() {
        try {
            await this.initialize();
            const records = await this.apiClient.getRecords();
            
            // 转换为原有格式（以ID为键的对象）
            const recordsMap = {};
            records.forEach(record => {
                recordsMap[record.record_id] = {
                    id: record.record_id,
                    title: record.title,
                    data: record.form_data,
                    type: record.record_type,
                    timestamp: record.created_at,
                    lastModified: record.updated_at
                };
            });
            
            return recordsMap;
        } catch (error) {
            console.error('获取记录失败:', error);
            return {};
        }
    }

    /**
     * 保存记录
     */
    async saveRecord(recordId, title, formData, recordType = 'draft') {
        try {
            await this.initialize();
            
            const result = await this.apiClient.saveRecord(recordId, title, formData, recordType);
            if (result.success) {
                console.log('记录保存成功:', recordId);
                return true;
            } else {
                console.error('记录保存失败:', result.message);
                return false;
            }
        } catch (error) {
            console.error('保存记录时出错:', error);
            return false;
        }
    }

    /**
     * 删除记录
     */
    async deleteRecord(recordId) {
        try {
            await this.initialize();
            
            const result = await this.apiClient.deleteRecord(recordId);
            if (result.success) {
                console.log('记录删除成功:', recordId);
                return true;
            } else {
                console.error('记录删除失败:', result.message);
                return false;
            }
        } catch (error) {
            console.error('删除记录时出错:', error);
            return false;
        }
    }

    /**
     * 获取特定记录
     */
    async getRecord(recordId) {
        try {
            await this.initialize();
            
            const record = await this.apiClient.getRecord(recordId);
            if (record) {
                return {
                    id: record.record_id,
                    title: record.title,
                    data: record.form_data,
                    type: record.record_type,
                    timestamp: record.created_at,
                    lastModified: record.updated_at
                };
            }
            return null;
        } catch (error) {
            console.error('获取记录失败:', error);
            return null;
        }
    }

    // ==================== 兼容性方法 ====================

    /**
     * 保存已提交记录（兼容原有逻辑）
     */
    async saveSubmittedRecord(formData) {
        if (this.isFormEmpty(formData)) {
            return false;
        }
        
        const recordId = this.generateId();
        const recordTitle = this.generateRecordTitle(formData);
        
        return await this.saveRecord(recordId, recordTitle, formData, 'submitted');
    }

    /**
     * 自动保存草稿（兼容原有逻辑）
     */
    async autoSaveDraft(formData) {
        if (this.isFormEmpty(formData)) {
            return false;
        }
        
        // 如果没有当前记录ID，创建新的草稿
        if (!this.currentRecordId) {
            this.currentRecordId = this.generateId();
        }
        
        const recordTitle = this.generateRecordTitle(formData);
        return await this.saveRecord(this.currentRecordId, recordTitle, formData, 'draft');
    }

    /**
     * 加载记录到表单（兼容原有逻辑）
     */
    async loadRecordToForm(recordId) {
        const record = await this.getRecord(recordId);
        if (!record) {
            return false;
        }

        // 设置当前记录ID
        this.currentRecordId = recordId;

        // 先重置动态表单元素
        this.resetDynamicFormElements();

        // 根据数据重建动态表单元素
        this.rebuildDynamicElements(record.data);

        // 恢复表单数据
        Object.keys(record.data).forEach(key => {
            const element = document.getElementById(key) || document.querySelector(`[name="${key}"]`);
            if (element && record.data[key]) {
                element.value = record.data[key];
                // 触发textarea自动调整高度
                if (element.tagName === 'TEXTAREA' && window.autoResizeTextarea) {
                    window.autoResizeTextarea(element);
                }
            }
        });

        return true;
    }

    /**
     * 重置动态表单元素
     */
    resetDynamicFormElements() {
        // 重置D1成员（保留固定的组长和前2个成员）
        const membersContainer = document.getElementById('dynamic-members-container');
        if (membersContainer) {
            membersContainer.innerHTML = '';
        }

        // 重置D3临时措施（保留第一个固定措施）
        const measuresContainer = document.getElementById('dynamic-measures-container');
        if (measuresContainer) {
            measuresContainer.innerHTML = '';
        }

        // 重置D4人机料法环测原因（每个类别保留第一个，删除其他）
        const causeCategories = ['man', 'machine', 'material', 'method', 'environment', 'measurement'];
        causeCategories.forEach(category => {
            const container = document.getElementById(`${category}-causes-container`);
            if (container) {
                const causes = container.querySelectorAll('.form-row-d4-analysis');
                // 保留第一个，删除其他
                for (let i = 1; i < causes.length; i++) {
                    causes[i].remove();
                }
            }
        });

        // 重置D5永久措施
        const d5Container = document.getElementById('dynamic-d5-measures-container');
        if (d5Container) {
            d5Container.innerHTML = '';
        }

        // 重置D6验证措施
        const d6Container = document.getElementById('dynamic-d6-verifications-container');
        if (d6Container) {
            d6Container.innerHTML = '';
        }

        // 重置D7预防措施
        const d7Container = document.getElementById('dynamic-d7-preventions-container');
        if (d7Container) {
            d7Container.innerHTML = '';
        }
    }

    /**
     * 根据数据重建动态表单元素
     */
    rebuildDynamicElements(formData) {
        // 重建D1成员
        this.rebuildMembers(formData);

        // 重建D3临时措施
        this.rebuildMeasures(formData);

        // 重建D4人机料法环测原因
        this.rebuildCauses(formData);

        // 重建D5永久措施
        this.rebuildD5Measures(formData);

        // 重建D6验证措施
        this.rebuildD6Verifications(formData);

        // 重建D7预防措施
        this.rebuildD7Preventions(formData);

        // 重建完成后，更新所有计数器和按钮状态
        this.updateAllCountersAndLimits();
    }

    /**
     * 更新所有计数器和按钮状态
     */
    updateAllCountersAndLimits() {
        try {
            // 更新计数器
            if (window.updateMemberCount) window.updateMemberCount();
            if (window.updateMeasureCount) window.updateMeasureCount();
            if (window.updateD5MeasureCount) window.updateD5MeasureCount();
            if (window.updateD6VerificationCount) window.updateD6VerificationCount();
            if (window.updateD7PreventionCount) window.updateD7PreventionCount();

            // 更新按钮限制状态
            if (window.checkMemberLimit) window.checkMemberLimit();
            if (window.checkMeasureLimit) window.checkMeasureLimit();
            if (window.checkD5MeasureLimit) window.checkD5MeasureLimit();
            if (window.checkD6VerificationLimit) window.checkD6VerificationLimit();
            if (window.checkD7PreventionLimit) window.checkD7PreventionLimit();
            if (window.checkAllCauseLimits) window.checkAllCauseLimits();

            console.log('所有计数器和按钮状态已更新');
        } catch (error) {
            console.error('更新计数器和按钮状态时出错:', error);
        }
    }

    /**
     * 重建D1成员
     */
    rebuildMembers(formData) {
        // 计算需要的动态成员数量（总成员数 - 3个固定成员）
        let maxMemberIndex = 0;
        Object.keys(formData).forEach(key => {
            const match = key.match(/^d1_member(\d+)_name$/);
            if (match) {
                const index = parseInt(match[1]);
                if (index > 3) { // 只考虑动态成员（索引>3）
                    maxMemberIndex = Math.max(maxMemberIndex, index);
                }
            }
        });

        // 添加动态成员
        for (let i = 4; i <= maxMemberIndex; i++) {
            if (formData[`d1_member${i}_name`]) {
                if (window.addMember) {
                    window.addMember();
                }
            }
        }
    }

    /**
     * 重建D3临时措施
     */
    rebuildMeasures(formData) {
        // 计算需要的动态措施数量
        let maxMeasureIndex = 0;
        Object.keys(formData).forEach(key => {
            const match = key.match(/^d3_scope(\d+)$/);
            if (match) {
                const index = parseInt(match[1]);
                if (index > 1) { // 只考虑动态措施（索引>1）
                    maxMeasureIndex = Math.max(maxMeasureIndex, index);
                }
            }
        });

        // 添加动态措施
        for (let i = 2; i <= maxMeasureIndex; i++) {
            if (formData[`d3_scope${i}`]) {
                if (window.addMeasure) {
                    window.addMeasure();
                }
            }
        }
    }

    /**
     * 重建D4人机料法环测原因
     */
    rebuildCauses(formData) {
        const causeCategories = ['man', 'machine', 'material', 'method', 'environment', 'measurement'];

        causeCategories.forEach(category => {
            // 计算该类别需要的原因数量
            let maxCauseIndex = 0;
            Object.keys(formData).forEach(key => {
                const match = key.match(new RegExp(`^d4_${category}(\\d+)$`));
                if (match) {
                    const index = parseInt(match[1]);
                    if (index > 1) { // 只考虑动态原因（索引>1）
                        maxCauseIndex = Math.max(maxCauseIndex, index);
                    }
                }
            });

            // 添加动态原因
            for (let i = 2; i <= maxCauseIndex; i++) {
                if (formData[`d4_${category}${i}`]) {
                    const labelMap = {
                        'man': '"人"原因',
                        'machine': '"机"原因',
                        'material': '"料"原因',
                        'method': '"法"原因',
                        'environment': '"环"原因',
                        'measurement': '"测"原因'
                    };
                    if (window.addCause) {
                        window.addCause(category, labelMap[category]);
                    }
                }
            }
        });
    }

    /**
     * 重建D5永久措施
     */
    rebuildD5Measures(formData) {
        let maxD5Index = 0;
        Object.keys(formData).forEach(key => {
            const match = key.match(/^d5_measure(\d+)$/);
            if (match) {
                const index = parseInt(match[1]);
                if (index > 1) {
                    maxD5Index = Math.max(maxD5Index, index);
                }
            }
        });

        for (let i = 2; i <= maxD5Index; i++) {
            if (formData[`d5_measure${i}`]) {
                if (window.addD5Measure) {
                    window.addD5Measure();
                }
            }
        }
    }

    /**
     * 重建D6验证措施
     */
    rebuildD6Verifications(formData) {
        let maxD6Index = 0;
        Object.keys(formData).forEach(key => {
            const match = key.match(/^d6_verification(\d+)$/);
            if (match) {
                const index = parseInt(match[1]);
                if (index > 1) {
                    maxD6Index = Math.max(maxD6Index, index);
                }
            }
        });

        for (let i = 2; i <= maxD6Index; i++) {
            if (formData[`d6_verification${i}`]) {
                if (window.addD6Verification) {
                    window.addD6Verification();
                }
            }
        }
    }

    /**
     * 重建D7预防措施
     */
    rebuildD7Preventions(formData) {
        let maxD7Index = 0;
        Object.keys(formData).forEach(key => {
            const match = key.match(/^d7_prevention(\d+)$/);
            if (match) {
                const index = parseInt(match[1]);
                if (index > 1) {
                    maxD7Index = Math.max(maxD7Index, index);
                }
            }
        });

        for (let i = 2; i <= maxD7Index; i++) {
            if (formData[`d7_prevention${i}`]) {
                if (window.addD7Prevention) {
                    window.addD7Prevention();
                }
            }
        }
    }

    /**
     * 清除当前记录ID
     */
    clearCurrentRecord() {
        this.currentRecordId = null;
    }

    /**
     * 获取当前记录ID
     */
    getCurrentRecordId() {
        return this.currentRecordId;
    }

    /**
     * 设置当前记录ID
     */
    setCurrentRecordId(recordId) {
        this.currentRecordId = recordId;
    }

    // ==================== 错误处理 ====================

    /**
     * 处理API错误
     */
    handleError(error, operation) {
        console.error(`${operation}失败:`, error);
        
        // 如果是认证错误，重定向到登录页面
        if (error.message && error.message.includes('登录')) {
            window.location.href = '/login';
            return;
        }
        
        // 显示用户友好的错误消息
        if (window.showToast) {
            window.showToast(`${operation}失败，请稍后重试`, 'error');
        } else {
            alert(`${operation}失败，请稍后重试`);
        }
    }
}

// 创建全局数据管理器实例
window.dataManager = new DataManager();

// 导出数据管理器类
window.DataManager = DataManager;
