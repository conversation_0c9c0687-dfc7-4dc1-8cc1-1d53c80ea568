"""
8D报告系统数据库模型和操作类
使用SQLite数据库，提供完整的数据管理功能
"""

import sqlite3
import json
import hashlib
import secrets
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Any
import os
from contextlib import contextmanager
from logger_config import logger, log_business_operation, log_error_operation


class Database:
    """数据库管理类"""
    
    def __init__(self, db_path: str = "8d_system.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库，创建表结构"""
        try:
            with self.get_connection() as conn:
                # 读取并执行SQL架构文件
                with open('database_schema.sql', 'r', encoding='utf-8') as f:
                    schema_sql = f.read()
                
                # 执行整个SQL脚本
                conn.executescript(schema_sql)
                
                conn.commit()
                logger.info("数据库初始化完成")
                log_business_operation('database_init', {'db_path': self.db_path})
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            log_error_operation('database_init_failed', e, {'db_path': self.db_path})
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        try:
            yield conn
        finally:
            conn.close()
    
    def execute_query(self, query: str, params: tuple = ()) -> List[sqlite3.Row]:
        """执行查询并返回结果"""
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            return cursor.fetchall()
    
    def execute_update(self, query: str, params: tuple = ()) -> int:
        """执行更新操作并返回影响的行数"""
        with self.get_connection() as conn:
            cursor = conn.execute(query, params)
            conn.commit()
            return cursor.rowcount


class UserManager:
    """用户管理类"""
    
    def __init__(self, db: Database):
        self.db = db
    
    def hash_password(self, password: str) -> str:
        """密码哈希"""
        salt = secrets.token_hex(16)
        password_hash = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
        return f"{salt}:{password_hash.hex()}"
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """验证密码"""
        try:
            salt, stored_hash = password_hash.split(':')
            password_hash_check = hashlib.pbkdf2_hmac('sha256', password.encode(), salt.encode(), 100000)
            return password_hash_check.hex() == stored_hash
        except:
            return False
    
    def create_user(self, username: str, email: str, password: str, full_name: str = None, department: str = None) -> Optional[int]:
        """创建新用户"""
        try:
            password_hash = self.hash_password(password)
            query = """
                INSERT INTO users (username, email, password_hash, full_name, department)
                VALUES (?, ?, ?, ?, ?)
            """
            with self.db.get_connection() as conn:
                cursor = conn.execute(query, (username, email, password_hash, full_name, department))
                conn.commit()
                user_id = cursor.lastrowid
                
                log_business_operation('user_created', {'user_id': user_id, 'username': username})
                return user_id
                
        except sqlite3.IntegrityError as e:
            if 'username' in str(e):
                raise ValueError("用户名已存在")
            elif 'email' in str(e):
                raise ValueError("邮箱已存在")
            else:
                raise ValueError("创建用户失败")
        except Exception as e:
            log_error_operation('user_creation_failed', e, {'username': username, 'email': email})
            raise
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """用户认证"""
        try:
            query = "SELECT * FROM users WHERE username = ? AND is_active = 1"
            users = self.db.execute_query(query, (username,))
            
            if users and self.verify_password(password, users[0]['password_hash']):
                user = dict(users[0])
                # 更新最后登录时间
                update_query = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?"
                self.db.execute_update(update_query, (user['id'],))
                
                log_business_operation('user_login', {'user_id': user['id'], 'username': username})
                return user
            
            return None
            
        except Exception as e:
            log_error_operation('user_authentication_failed', e, {'username': username})
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """根据ID获取用户信息"""
        query = "SELECT * FROM users WHERE id = ? AND is_active = 1"
        users = self.db.execute_query(query, (user_id,))
        return dict(users[0]) if users else None


class SessionManager:
    """会话管理类"""
    
    def __init__(self, db: Database):
        self.db = db
    
    def create_session(self, user_id: int, ip_address: str = None, user_agent: str = None) -> str:
        """创建用户会话"""
        session_id = secrets.token_urlsafe(32)
        expires_at = datetime.now() + timedelta(days=7)  # 7天过期
        
        query = """
            INSERT INTO user_sessions (session_id, user_id, ip_address, user_agent, expires_at)
            VALUES (?, ?, ?, ?, ?)
        """
        self.db.execute_update(query, (session_id, user_id, ip_address, user_agent, expires_at))
        
        log_business_operation('session_created', {'user_id': user_id, 'session_id': session_id})
        return session_id
    
    def validate_session(self, session_id: str) -> Optional[Dict]:
        """验证会话有效性"""
        query = """
            SELECT s.*, u.username, u.full_name 
            FROM user_sessions s 
            JOIN users u ON s.user_id = u.id 
            WHERE s.session_id = ? AND s.is_active = 1 AND s.expires_at > CURRENT_TIMESTAMP
        """
        sessions = self.db.execute_query(query, (session_id,))
        return dict(sessions[0]) if sessions else None
    
    def invalidate_session(self, session_id: str):
        """使会话失效"""
        query = "UPDATE user_sessions SET is_active = 0 WHERE session_id = ?"
        self.db.execute_update(query, (session_id,))
        log_business_operation('session_invalidated', {'session_id': session_id})


class FormRecordManager:
    """表单记录管理类"""
    
    def __init__(self, db: Database):
        self.db = db
    
    def save_record(self, user_id: int, record_id: str, title: str, form_data: Dict, record_type: str = 'draft') -> bool:
        """保存表单记录"""
        try:
            form_data_json = json.dumps(form_data, ensure_ascii=False)
            
            # 检查记录是否已存在
            existing = self.db.execute_query(
                "SELECT id FROM form_records WHERE record_id = ? AND user_id = ?", 
                (record_id, user_id)
            )
            
            if existing:
                # 更新现有记录
                query = """
                    UPDATE form_records 
                    SET title = ?, form_data = ?, record_type = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE record_id = ? AND user_id = ?
                """
                self.db.execute_update(query, (title, form_data_json, record_type, record_id, user_id))
            else:
                # 创建新记录
                query = """
                    INSERT INTO form_records (record_id, user_id, title, form_data, record_type)
                    VALUES (?, ?, ?, ?, ?)
                """
                self.db.execute_update(query, (record_id, user_id, title, form_data_json, record_type))
            
            log_business_operation('form_record_saved', {
                'user_id': user_id, 'record_id': record_id, 'record_type': record_type
            })
            return True
            
        except Exception as e:
            log_error_operation('form_record_save_failed', e, {
                'user_id': user_id, 'record_id': record_id
            })
            return False
    
    def get_user_records(self, user_id: int, record_type: str = None) -> List[Dict]:
        """获取用户的表单记录"""
        if record_type:
            query = """
                SELECT * FROM form_records 
                WHERE user_id = ? AND record_type = ? 
                ORDER BY updated_at DESC
            """
            records = self.db.execute_query(query, (user_id, record_type))
        else:
            query = """
                SELECT * FROM form_records 
                WHERE user_id = ? 
                ORDER BY updated_at DESC
            """
            records = self.db.execute_query(query, (user_id,))
        
        result = []
        for record in records:
            record_dict = dict(record)
            record_dict['form_data'] = json.loads(record_dict['form_data'])
            result.append(record_dict)
        
        return result
    
    def get_record(self, user_id: int, record_id: str) -> Optional[Dict]:
        """获取特定记录"""
        query = "SELECT * FROM form_records WHERE record_id = ? AND user_id = ?"
        records = self.db.execute_query(query, (record_id, user_id))
        
        if records:
            record_dict = dict(records[0])
            record_dict['form_data'] = json.loads(record_dict['form_data'])
            return record_dict
        
        return None
    
    def delete_record(self, user_id: int, record_id: str) -> bool:
        """删除记录"""
        try:
            query = "DELETE FROM form_records WHERE record_id = ? AND user_id = ?"
            affected_rows = self.db.execute_update(query, (record_id, user_id))
            
            if affected_rows > 0:
                log_business_operation('form_record_deleted', {
                    'user_id': user_id, 'record_id': record_id
                })
                return True
            return False
            
        except Exception as e:
            log_error_operation('form_record_delete_failed', e, {
                'user_id': user_id, 'record_id': record_id
            })
            return False


class ConversationManager:
    """对话管理类"""

    def __init__(self, db: Database):
        self.db = db

    def create_conversation(self, user_id: int, conversation_id: str, title: str, description: str = None) -> bool:
        """创建新对话"""
        try:
            query = """
                INSERT INTO conversations (conversation_id, user_id, title, description)
                VALUES (?, ?, ?, ?)
            """
            self.db.execute_update(query, (conversation_id, user_id, title, description))

            log_business_operation('conversation_created', {
                'user_id': user_id, 'conversation_id': conversation_id
            })
            return True

        except Exception as e:
            log_error_operation('conversation_creation_failed', e, {
                'user_id': user_id, 'conversation_id': conversation_id
            })
            return False

    def get_user_conversations(self, user_id: int) -> List[Dict]:
        """获取用户的所有对话"""
        query = """
            SELECT * FROM conversations
            WHERE user_id = ? AND status = 'active'
            ORDER BY updated_at DESC
        """
        conversations = self.db.execute_query(query, (user_id,))
        return [dict(conv) for conv in conversations]

    def get_conversation(self, user_id: int, conversation_id: str) -> Optional[Dict]:
        """获取特定对话"""
        query = """
            SELECT * FROM conversations
            WHERE conversation_id = ? AND user_id = ? AND status = 'active'
        """
        conversations = self.db.execute_query(query, (conversation_id, user_id))
        return dict(conversations[0]) if conversations else None

    def update_conversation(self, user_id: int, conversation_id: str, title: str = None, description: str = None) -> bool:
        """更新对话信息"""
        try:
            updates = []
            params = []

            if title is not None:
                updates.append("title = ?")
                params.append(title)

            if description is not None:
                updates.append("description = ?")
                params.append(description)

            if not updates:
                return True

            query = f"""
                UPDATE conversations
                SET {', '.join(updates)}, updated_at = CURRENT_TIMESTAMP
                WHERE conversation_id = ? AND user_id = ?
            """
            params.extend([conversation_id, user_id])

            affected_rows = self.db.execute_update(query, tuple(params))
            return affected_rows > 0

        except Exception as e:
            log_error_operation('conversation_update_failed', e, {
                'user_id': user_id, 'conversation_id': conversation_id
            })
            return False


class FormVersionManager:
    """表单版本管理类"""

    def __init__(self, db: Database):
        self.db = db

    def add_version(self, conversation_id: int, user_id: int, version_id: str, version_name: str,
                    form_data: Dict, created_by: str = 'user', modification_notes: str = None) -> bool:
        """添加新版本"""
        try:
            # 先将该对话的所有版本设为非活跃
            self.db.execute_update(
                "UPDATE form_versions SET is_active = 0 WHERE conversation_id = ?",
                (conversation_id,)
            )

            # 添加新版本并设为活跃
            form_data_json = json.dumps(form_data, ensure_ascii=False)
            query = """
                INSERT INTO form_versions
                (version_id, conversation_id, user_id, version_name, form_data,
                 modification_notes, created_by, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, 1)
            """
            self.db.execute_update(query, (
                version_id, conversation_id, user_id, version_name,
                form_data_json, modification_notes, created_by
            ))

            log_business_operation('form_version_added', {
                'user_id': user_id, 'conversation_id': conversation_id, 'version_id': version_id
            })
            return True

        except Exception as e:
            log_error_operation('form_version_add_failed', e, {
                'user_id': user_id, 'conversation_id': conversation_id, 'version_id': version_id
            })
            return False

    def get_conversation_versions(self, conversation_id: int, user_id: int) -> List[Dict]:
        """获取对话的所有版本"""
        query = """
            SELECT * FROM form_versions
            WHERE conversation_id = ? AND user_id = ?
            ORDER BY created_at DESC
        """
        versions = self.db.execute_query(query, (conversation_id, user_id))

        result = []
        for version in versions:
            version_dict = dict(version)
            version_dict['form_data'] = json.loads(version_dict['form_data'])
            result.append(version_dict)

        return result

    def get_active_version(self, conversation_id: int, user_id: int) -> Optional[Dict]:
        """获取活跃版本"""
        query = """
            SELECT * FROM form_versions
            WHERE conversation_id = ? AND user_id = ? AND is_active = 1
        """
        versions = self.db.execute_query(query, (conversation_id, user_id))

        if versions:
            version_dict = dict(versions[0])
            version_dict['form_data'] = json.loads(version_dict['form_data'])
            return version_dict

        return None

    def set_active_version(self, conversation_id: int, user_id: int, version_id: str) -> bool:
        """设置活跃版本"""
        try:
            # 先将所有版本设为非活跃
            self.db.execute_update(
                "UPDATE form_versions SET is_active = 0 WHERE conversation_id = ?",
                (conversation_id,)
            )

            # 设置指定版本为活跃
            affected_rows = self.db.execute_update(
                "UPDATE form_versions SET is_active = 1 WHERE version_id = ? AND conversation_id = ? AND user_id = ?",
                (version_id, conversation_id, user_id)
            )

            return affected_rows > 0

        except Exception as e:
            log_error_operation('set_active_version_failed', e, {
                'user_id': user_id, 'conversation_id': conversation_id, 'version_id': version_id
            })
            return False

    def update_version(self, conversation_id: int, user_id: int, version_id: str,
                      form_data: Dict = None, version_name: str = None, modification_notes: str = None) -> bool:
        """更新版本数据"""
        try:
            # 构建更新字段
            update_fields = []
            update_values = []

            if form_data is not None:
                update_fields.append("form_data = ?")
                update_values.append(json.dumps(form_data, ensure_ascii=False))

            if version_name is not None:
                update_fields.append("version_name = ?")
                update_values.append(version_name)

            if modification_notes is not None:
                update_fields.append("modification_notes = ?")
                update_values.append(modification_notes)

            if not update_fields:
                return True  # 没有需要更新的字段

            # 构建SQL查询
            query = f"""
                UPDATE form_versions
                SET {', '.join(update_fields)}
                WHERE version_id = ? AND conversation_id = ? AND user_id = ?
            """

            # 添加WHERE条件的参数
            update_values.extend([version_id, conversation_id, user_id])

            affected_rows = self.db.execute_update(query, tuple(update_values))

            if affected_rows > 0:
                log_business_operation('version_updated', {
                    'user_id': user_id, 'conversation_id': conversation_id, 'version_id': version_id,
                    'updated_fields': len(update_fields) - 1  # 减去updated_at字段
                })
                return True
            else:
                return False

        except Exception as e:
            log_error_operation('update_version_failed', e, {
                'user_id': user_id, 'conversation_id': conversation_id, 'version_id': version_id
            })
            return False

    def get_version(self, conversation_id: int, user_id: int, version_id: str) -> Dict:
        """获取指定版本"""
        query = """
            SELECT * FROM form_versions
            WHERE conversation_id = ? AND user_id = ? AND version_id = ?
        """
        result = self.db.execute_query(query, (conversation_id, user_id, version_id))

        if result:
            version = dict(result[0])
            if version['form_data']:
                try:
                    version['form_data'] = json.loads(version['form_data'])
                except json.JSONDecodeError:
                    version['form_data'] = {}
            return version
        return None


class ChatMessageManager:
    """聊天消息管理类"""

    def __init__(self, db: Database):
        self.db = db

    def add_message(self, conversation_id: int, user_id: int, message_id: str, sender: str,
                    message: str, version_before: str = None, version_after: str = None) -> bool:
        """添加聊天消息"""
        try:
            query = """
                INSERT INTO chat_messages
                (message_id, conversation_id, user_id, sender, message, version_before, version_after)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            self.db.execute_update(query, (
                message_id, conversation_id, user_id, sender, message, version_before, version_after
            ))

            log_business_operation('chat_message_added', {
                'user_id': user_id, 'conversation_id': conversation_id, 'message_id': message_id
            })
            return True

        except Exception as e:
            log_error_operation('chat_message_add_failed', e, {
                'user_id': user_id, 'conversation_id': conversation_id, 'message_id': message_id
            })
            return False

    def get_conversation_messages(self, conversation_id: int, user_id: int) -> List[Dict]:
        """获取对话的所有消息"""
        query = """
            SELECT * FROM chat_messages
            WHERE conversation_id = ? AND user_id = ?
            ORDER BY created_at ASC
        """
        messages = self.db.execute_query(query, (conversation_id, user_id))
        return [dict(msg) for msg in messages]


# 全局数据库实例
db_instance = Database()
user_manager = UserManager(db_instance)
session_manager = SessionManager(db_instance)
form_record_manager = FormRecordManager(db_instance)
conversation_manager = ConversationManager(db_instance)
form_version_manager = FormVersionManager(db_instance)
chat_message_manager = ChatMessageManager(db_instance)
