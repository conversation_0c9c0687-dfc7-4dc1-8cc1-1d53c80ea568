#!/usr/bin/env python3
"""
测试版本切换时动态表单元素的正确性
"""

import requests
import json

BASE_URL = 'http://localhost:5555'

def login_and_get_cookies():
    """登录并获取cookies"""
    data = {
        'username': 'testuser',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/auth/login', json=data)
        if response.json().get('success'):
            return response.cookies
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def create_test_conversation(cookies):
    """创建测试对话"""
    data = {
        'conversation_id': 'test_conv_001',
        'title': '测试版本切换',
        'description': '测试动态表单元素在版本切换时的正确性'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations', json=data, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"创建对话失败: {e}")
        return False

def create_version_with_single_causes(cookies):
    """创建只有单个原因的版本"""
    form_data = {
        'problem_description': '测试问题描述',
        'product_name': '测试产品',
        'd4_man1': '操作员培训不足',
        'd4_man1_judgment': '主因',
        'd4_man1_evidence': '培训记录缺失',
        'd4_machine1': '设备维护不当',
        'd4_machine1_judgment': '主因',
        'd4_machine1_evidence': '维护记录显示异常',
        'd4_material1': '材料质量问题',
        'd4_material1_judgment': '次因',
        'd4_material1_evidence': '供应商检测报告异常'
    }
    
    data = {
        'version_id': 'v_single_001',
        'version_name': '单个原因版本',
        'form_data': form_data,
        'created_by': 'user'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/test_conv_001/versions', json=data, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"创建单个原因版本失败: {e}")
        return False

def create_version_with_multiple_causes(cookies):
    """创建有多个原因的版本"""
    form_data = {
        'problem_description': '测试问题描述',
        'product_name': '测试产品',
        # 人原因 - 2个
        'd4_man1': '操作员培训不足',
        'd4_man1_judgment': '主因',
        'd4_man1_evidence': '培训记录缺失',
        'd4_man2': '操作员经验不足',
        'd4_man2_judgment': '次因',
        'd4_man2_evidence': '新员工上岗记录',
        # 机原因 - 2个
        'd4_machine1': '设备维护不当',
        'd4_machine1_judgment': '主因',
        'd4_machine1_evidence': '维护记录显示异常',
        'd4_machine2': '设备老化严重',
        'd4_machine2_judgment': '次因',
        'd4_machine2_evidence': '设备使用年限超标',
        # 料原因 - 2个
        'd4_material1': '材料质量问题',
        'd4_material1_judgment': '次因',
        'd4_material1_evidence': '供应商检测报告异常',
        'd4_material2': '材料规格不符',
        'd4_material2_judgment': '排除',
        'd4_material2_evidence': '规格检查合格'
    }
    
    data = {
        'version_id': 'v_multiple_001',
        'version_name': '多个原因版本',
        'form_data': form_data,
        'created_by': 'user'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/test_conv_001/versions', json=data, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"创建多个原因版本失败: {e}")
        return False

def get_versions(cookies):
    """获取版本列表"""
    try:
        response = requests.get(f'{BASE_URL}/api/conversations/test_conv_001/versions', cookies=cookies)
        result = response.json()
        if result.get('success'):
            return result.get('versions', [])
        return []
    except Exception as e:
        print(f"获取版本列表失败: {e}")
        return []

def set_active_version(cookies, version_id):
    """设置活跃版本"""
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/test_conv_001/versions/{version_id}/activate', cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"设置活跃版本失败: {e}")
        return False

def get_active_version(cookies):
    """获取活跃版本"""
    try:
        response = requests.get(f'{BASE_URL}/api/conversations/test_conv_001/versions/active', cookies=cookies)
        result = response.json()
        if result.get('success'):
            return result.get('version')
        return None
    except Exception as e:
        print(f"获取活跃版本失败: {e}")
        return None

def main():
    """主测试函数"""
    print("开始版本切换测试...")
    
    # 登录
    cookies = login_and_get_cookies()
    if not cookies:
        print("✗ 登录失败")
        return
    print("✓ 登录成功")
    
    # 创建测试对话
    if create_test_conversation(cookies):
        print("✓ 创建测试对话成功")
    else:
        print("✗ 创建测试对话失败")
        return
    
    # 创建单个原因版本
    if create_version_with_single_causes(cookies):
        print("✓ 创建单个原因版本成功")
    else:
        print("✗ 创建单个原因版本失败")
        return
    
    # 创建多个原因版本
    if create_version_with_multiple_causes(cookies):
        print("✓ 创建多个原因版本成功")
    else:
        print("✗ 创建多个原因版本失败")
        return
    
    # 获取版本列表
    versions = get_versions(cookies)
    print(f"✓ 获取到 {len(versions)} 个版本")
    
    for version in versions:
        print(f"  - {version['version_name']} (ID: {version['version_id']})")
    
    # 测试版本切换
    print("\n开始测试版本切换...")
    
    # 切换到单个原因版本
    single_version = next((v for v in versions if v['version_name'] == '单个原因版本'), None)
    if single_version and set_active_version(cookies, single_version['version_id']):
        print("✓ 切换到单个原因版本成功")
        active = get_active_version(cookies)
        if active:
            form_data = active['form_data']
            man_causes = [k for k in form_data.keys() if k.startswith('d4_man') and not k.endswith('_judgment') and not k.endswith('_evidence')]
            print(f"  当前人原因数量: {len(man_causes)}")
    
    # 切换到多个原因版本
    multiple_version = next((v for v in versions if v['version_name'] == '多个原因版本'), None)
    if multiple_version and set_active_version(cookies, multiple_version['version_id']):
        print("✓ 切换到多个原因版本成功")
        active = get_active_version(cookies)
        if active:
            form_data = active['form_data']
            man_causes = [k for k in form_data.keys() if k.startswith('d4_man') and not k.endswith('_judgment') and not k.endswith('_evidence')]
            print(f"  当前人原因数量: {len(man_causes)}")
    
    # 再次切换回单个原因版本
    if single_version and set_active_version(cookies, single_version['version_id']):
        print("✓ 再次切换到单个原因版本成功")
        active = get_active_version(cookies)
        if active:
            form_data = active['form_data']
            man_causes = [k for k in form_data.keys() if k.startswith('d4_man') and not k.endswith('_judgment') and not k.endswith('_evidence')]
            print(f"  当前人原因数量: {len(man_causes)}")
    
    print("\n版本切换测试完成!")
    print("请在浏览器中手动验证前端界面的动态元素数量是否正确")

if __name__ == '__main__':
    main()
