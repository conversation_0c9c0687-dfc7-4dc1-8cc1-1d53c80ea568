#!/usr/bin/env python3
"""
测试自动保存功能
验证数据是否正确保存到后端并能够持久化
"""

import requests
import json
import time

BASE_URL = 'http://localhost:5555'

def login_and_get_cookies():
    """登录并获取cookies"""
    data = {
        'username': 'testuser',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/auth/login', json=data)
        if response.json().get('success'):
            return response.cookies
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def create_test_conversation(cookies):
    """创建测试对话"""
    import time
    timestamp = int(time.time())
    data = {
        'conversation_id': f'autosave_test_conv_{timestamp}',
        'title': f'自动保存测试对话_{timestamp}',
        'description': '测试自动保存功能的对话'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations', json=data, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"创建对话失败: {e}")
        return False

def create_initial_version(cookies):
    """创建初始版本"""
    form_data = {
        'problem_description': '初始问题描述',
        'product_name': '测试产品',
        'd4_man1': '初始人员原因',
        'd5_measure1': '初始纠正措施'
    }
    
    data = {
        'version_id': 'autosave_v1',
        'version_name': '初始版本',
        'form_data': form_data,
        'created_by': 'user'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/autosave_test_conv/versions', 
                               json=data, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"创建初始版本失败: {e}")
        return False

def update_version_data(cookies, updated_data):
    """更新版本数据（模拟自动保存）"""
    try:
        response = requests.put(f'{BASE_URL}/api/conversations/autosave_test_conv/versions/autosave_v1',
                              json={'form_data': updated_data}, cookies=cookies)
        return response.json().get('success', False)
    except Exception as e:
        print(f"更新版本数据失败: {e}")
        return False

def get_version_data(cookies):
    """获取版本数据"""
    try:
        response = requests.get(f'{BASE_URL}/api/conversations/autosave_test_conv/versions/active', 
                              cookies=cookies)
        result = response.json()
        if result.get('success'):
            return result.get('version', {}).get('form_data', {})
        return {}
    except Exception as e:
        print(f"获取版本数据失败: {e}")
        return {}

def test_data_persistence(cookies):
    """测试数据持久化"""
    print("=== 测试数据持久化 ===")
    
    # 第一次更新
    updated_data_1 = {
        'problem_description': '更新后的问题描述 - 第一次',
        'product_name': '更新后的产品名称',
        'd4_man1': '更新后的人员原因',
        'd4_man2': '新增的第二个人员原因',
        'd5_measure1': '更新后的纠正措施',
        'd5_measure2': '新增的第二个措施'
    }
    
    print("1. 执行第一次数据更新...")
    if update_version_data(cookies, updated_data_1):
        print("✓ 第一次更新成功")
        
        # 验证数据
        retrieved_data = get_version_data(cookies)
        if retrieved_data.get('problem_description') == updated_data_1['problem_description']:
            print("✓ 第一次数据验证成功")
        else:
            print("❌ 第一次数据验证失败")
            return False
    else:
        print("❌ 第一次更新失败")
        return False
    
    # 等待一段时间，模拟用户继续编辑
    time.sleep(1)
    
    # 第二次更新
    updated_data_2 = {
        'problem_description': '更新后的问题描述 - 第二次',
        'product_name': '再次更新的产品名称',
        'd4_man1': '再次更新的人员原因',
        'd4_man2': '再次更新的第二个人员原因',
        'd4_man3': '新增的第三个人员原因',
        'd5_measure1': '再次更新的纠正措施',
        'd5_measure2': '再次更新的第二个措施',
        'd5_measure3': '新增的第三个措施'
    }
    
    print("2. 执行第二次数据更新...")
    if update_version_data(cookies, updated_data_2):
        print("✓ 第二次更新成功")
        
        # 验证数据
        retrieved_data = get_version_data(cookies)
        if retrieved_data.get('problem_description') == updated_data_2['problem_description']:
            print("✓ 第二次数据验证成功")
        else:
            print("❌ 第二次数据验证失败")
            return False
    else:
        print("❌ 第二次更新失败")
        return False
    
    # 最终验证：检查所有字段
    print("3. 执行最终数据完整性验证...")
    final_data = get_version_data(cookies)
    
    success = True
    for key, expected_value in updated_data_2.items():
        actual_value = final_data.get(key)
        if actual_value != expected_value:
            print(f"❌ 字段 {key} 不匹配: 期望 '{expected_value}', 实际 '{actual_value}'")
            success = False
    
    if success:
        print("✓ 最终数据完整性验证成功")
        print(f"✓ 共保存了 {len(final_data)} 个字段")
    
    return success

def test_session_persistence(cookies):
    """测试会话持久化（模拟刷新页面）"""
    print("\n=== 测试会话持久化 ===")
    
    # 获取当前数据作为基准
    original_data = get_version_data(cookies)
    print(f"原始数据包含 {len(original_data)} 个字段")
    
    # 模拟页面刷新后重新获取数据
    print("模拟页面刷新，重新获取数据...")
    refreshed_data = get_version_data(cookies)
    
    # 比较数据
    if original_data == refreshed_data:
        print("✓ 会话持久化测试成功，数据完全一致")
        return True
    else:
        print("❌ 会话持久化测试失败，数据不一致")
        print(f"原始数据: {len(original_data)} 个字段")
        print(f"刷新后数据: {len(refreshed_data)} 个字段")
        
        # 详细比较
        for key in set(original_data.keys()) | set(refreshed_data.keys()):
            orig_val = original_data.get(key)
            refresh_val = refreshed_data.get(key)
            if orig_val != refresh_val:
                print(f"  差异字段 {key}: 原始='{orig_val}', 刷新='{refresh_val}'")
        
        return False

def main():
    """主测试函数"""
    print("开始自动保存功能测试...")
    
    # 登录
    cookies = login_and_get_cookies()
    if not cookies:
        print("❌ 登录失败")
        return
    print("✓ 登录成功")
    
    # 创建测试对话
    if create_test_conversation(cookies):
        print("✓ 创建测试对话成功")
    else:
        print("❌ 创建测试对话失败")
        return
    
    # 创建初始版本
    if create_initial_version(cookies):
        print("✓ 创建初始版本成功")
    else:
        print("❌ 创建初始版本失败")
        return
    
    # 测试数据持久化
    if not test_data_persistence(cookies):
        print("❌ 数据持久化测试失败")
        return
    
    # 测试会话持久化
    if not test_session_persistence(cookies):
        print("❌ 会话持久化测试失败")
        return
    
    print("\n🎉 所有自动保存测试通过！")
    print("现在可以在浏览器中验证：")
    print("1. 登录系统并打开测试对话")
    print("2. 修改表单内容，观察右上角的保存状态指示器")
    print("3. 刷新页面，验证数据是否保持")
    print("4. 登出再登录，验证数据是否持久化")

if __name__ == '__main__':
    main()
