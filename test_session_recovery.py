#!/usr/bin/env python3
"""
测试页面会话恢复功能
验证页面刷新后数据是否能正确保持
"""

import requests
import json
import time
import sys
from datetime import datetime

BASE_URL = 'http://localhost:5000'

def test_user_login():
    """测试用户登录"""
    print("=== 测试用户登录 ===")
    
    login_data = {
        'username': 'testuser',
        'password': 'testpass123'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/auth/login', json=login_data)
        result = response.json()
        
        if result.get('success'):
            print("✓ 用户登录成功")
            return response.cookies
        else:
            print(f"❌ 用户登录失败: {result.get('message')}")
            return None
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return None

def create_test_conversation(cookies):
    """创建测试对话"""
    print("\n=== 创建测试对话 ===")
    
    conversation_data = {
        'conversation_id': 'session_test_conv',
        'title': '会话恢复测试对话',
        'description': '用于测试页面刷新后会话恢复功能'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations', 
                               json=conversation_data, cookies=cookies)
        result = response.json()
        
        if result.get('success'):
            print("✓ 测试对话创建成功")
            return True
        else:
            print(f"❌ 测试对话创建失败: {result.get('message')}")
            return False
    except Exception as e:
        print(f"❌ 创建对话请求失败: {e}")
        return False

def create_test_version(cookies):
    """创建测试版本"""
    print("\n=== 创建测试版本 ===")
    
    test_form_data = {
        'problem_description': '测试问题描述',
        'team_members': '测试团队成员',
        'problem_date': '2024-01-01',
        'd1_team': '测试D1团队',
        'd2_problem': '测试D2问题描述',
        'd3_containment': '测试D3遏制措施'
    }
    
    version_data = {
        'version_id': 'session_test_v1',
        'version_name': '会话测试版本1',
        'form_data': test_form_data,
        'modification_notes': '初始测试版本'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/conversations/session_test_conv/versions',
                               json=version_data, cookies=cookies)
        result = response.json()
        
        if result.get('success'):
            print("✓ 测试版本创建成功")
            return True
        else:
            print(f"❌ 测试版本创建失败: {result.get('message')}")
            return False
    except Exception as e:
        print(f"❌ 创建版本请求失败: {e}")
        return False

def update_form_data(cookies, updated_data):
    """更新表单数据（模拟自动保存）"""
    print("\n=== 更新表单数据 ===")
    
    try:
        response = requests.put(f'{BASE_URL}/api/conversations/session_test_conv/versions/session_test_v1',
                              json={'form_data': updated_data}, cookies=cookies)
        result = response.json()
        
        if result.get('success'):
            print("✓ 表单数据更新成功")
            return True
        else:
            print(f"❌ 表单数据更新失败: {result.get('message')}")
            return False
    except Exception as e:
        print(f"❌ 更新数据请求失败: {e}")
        return False

def get_current_data(cookies):
    """获取当前数据"""
    try:
        response = requests.get(f'{BASE_URL}/api/conversations/session_test_conv/versions/active',
                              cookies=cookies)
        result = response.json()
        
        if result.get('success'):
            return result.get('version', {}).get('form_data', {})
        return {}
    except Exception as e:
        print(f"获取数据失败: {e}")
        return {}

def test_session_persistence(cookies):
    """测试会话持久化"""
    print("\n=== 测试会话持久化 ===")
    
    # 1. 获取初始数据
    original_data = get_current_data(cookies)
    print(f"原始数据字段数: {len(original_data)}")
    
    # 2. 更新一些数据
    updated_data = original_data.copy()
    updated_data.update({
        'problem_description': f'更新的问题描述 - {datetime.now().strftime("%H:%M:%S")}',
        'd4_root_cause': '新增的根本原因分析',
        'd5_corrective_actions': '新增的纠正措施'
    })
    
    if update_form_data(cookies, updated_data):
        print("✓ 数据更新成功")
    else:
        print("❌ 数据更新失败")
        return False
    
    # 3. 模拟页面刷新 - 重新获取数据
    print("模拟页面刷新，重新获取数据...")
    time.sleep(1)  # 等待数据保存
    
    refreshed_data = get_current_data(cookies)
    
    # 4. 比较数据
    if updated_data == refreshed_data:
        print("✓ 会话持久化测试成功 - 数据完全一致")
        return True
    else:
        print("❌ 会话持久化测试失败 - 数据不一致")
        print(f"更新后数据字段数: {len(updated_data)}")
        print(f"刷新后数据字段数: {len(refreshed_data)}")
        
        # 详细比较
        all_keys = set(updated_data.keys()) | set(refreshed_data.keys())
        for key in all_keys:
            updated_val = updated_data.get(key)
            refreshed_val = refreshed_data.get(key)
            if updated_val != refreshed_val:
                print(f"  差异字段 {key}:")
                print(f"    更新后: '{updated_val}'")
                print(f"    刷新后: '{refreshed_val}'")
        
        return False

def test_auto_save_context_recovery(cookies):
    """测试自动保存上下文恢复"""
    print("\n=== 测试自动保存上下文恢复 ===")
    
    # 这个测试需要在浏览器中进行，这里只能测试API层面
    try:
        # 获取对话列表
        response = requests.get(f'{BASE_URL}/api/conversations', cookies=cookies)
        result = response.json()
        
        if result.get('success'):
            conversations = result.get('conversations', [])
            if conversations:
                print(f"✓ 成功获取 {len(conversations)} 个对话")
                
                # 检查测试对话是否存在
                test_conv = next((c for c in conversations if c['conversation_id'] == 'session_test_conv'), None)
                if test_conv:
                    print("✓ 测试对话存在于对话列表中")
                    return True
                else:
                    print("❌ 测试对话不在对话列表中")
                    return False
            else:
                print("❌ 对话列表为空")
                return False
        else:
            print(f"❌ 获取对话列表失败: {result.get('message')}")
            return False
    except Exception as e:
        print(f"❌ 测试上下文恢复失败: {e}")
        return False

def cleanup_test_data(cookies):
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    try:
        # 删除测试对话
        response = requests.delete(f'{BASE_URL}/api/conversations/session_test_conv', cookies=cookies)
        result = response.json()
        
        if result.get('success'):
            print("✓ 测试数据清理成功")
        else:
            print(f"⚠️  测试数据清理失败: {result.get('message')}")
    except Exception as e:
        print(f"⚠️  清理测试数据时出错: {e}")

def main():
    """主测试流程"""
    print("开始会话恢复功能测试")
    print("=" * 50)
    
    # 1. 用户登录
    cookies = test_user_login()
    if not cookies:
        print("无法继续测试，用户登录失败")
        return False
    
    # 2. 创建测试对话
    if not create_test_conversation(cookies):
        print("无法继续测试，创建对话失败")
        return False
    
    # 3. 创建测试版本
    if not create_test_version(cookies):
        print("无法继续测试，创建版本失败")
        return False
    
    # 4. 测试会话持久化
    session_test_passed = test_session_persistence(cookies)
    
    # 5. 测试自动保存上下文恢复
    context_test_passed = test_auto_save_context_recovery(cookies)
    
    # 6. 清理测试数据
    cleanup_test_data(cookies)
    
    # 7. 总结结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"会话持久化测试: {'✓ 通过' if session_test_passed else '❌ 失败'}")
    print(f"上下文恢复测试: {'✓ 通过' if context_test_passed else '❌ 失败'}")
    
    overall_success = session_test_passed and context_test_passed
    print(f"总体测试结果: {'✓ 全部通过' if overall_success else '❌ 存在失败'}")
    
    return overall_success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
