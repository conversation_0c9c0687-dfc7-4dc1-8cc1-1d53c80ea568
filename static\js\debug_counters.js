/**
 * 调试计数器和按钮状态的工具函数
 * 在浏览器控制台中使用
 */

window.debugCounters = {
    /**
     * 检查所有计数器的当前状态
     */
    checkAllCounters() {
        console.log('=== 计数器状态检查 ===');
        
        // D1成员计数
        const memberCount = document.querySelectorAll('#dynamic-members-container .form-row-member').length + 3; // 3个固定成员
        const memberDisplay = document.querySelector('.member-count-display');
        console.log(`D1成员: 实际${memberCount}个, 显示${memberDisplay ? memberDisplay.textContent : '未找到'}`);
        
        // D3临时措施计数
        const measureCount = document.querySelectorAll('#dynamic-measures-container .form-row-measure').length + 1; // 1个固定措施
        const measureDisplay = document.querySelector('.measure-count-display');
        console.log(`D3措施: 实际${measureCount}个, 显示${measureDisplay ? measureDisplay.textContent : '未找到'}`);
        
        // D5永久措施计数
        const d5Count = document.querySelectorAll('#dynamic-d5-measures-container .form-row-d5-measure').length + 1; // 1个固定措施
        const d5Display = document.querySelector('.d5-measure-count-display');
        console.log(`D5措施: 实际${d5Count}个, 显示${d5Display ? d5Display.textContent : '未找到'}`);
        
        // D6验证措施计数
        const d6Count = document.querySelectorAll('#dynamic-d6-verifications-container .form-row-d6-verification').length + 1; // 1个固定验证
        const d6Display = document.querySelector('.d6-verification-count-display');
        console.log(`D6验证: 实际${d6Count}个, 显示${d6Display ? d6Display.textContent : '未找到'}`);
        
        // D7预防措施计数
        const d7Count = document.querySelectorAll('#dynamic-d7-preventions-container .form-row-d7-prevention').length + 1; // 1个固定预防
        const d7Display = document.querySelector('.d7-prevention-count-display');
        console.log(`D7预防: 实际${d7Count}个, 显示${d7Display ? d7Display.textContent : '未找到'}`);
        
        console.log('=== 计数器检查完成 ===');
    },
    
    /**
     * 检查所有增加按钮的状态
     */
    checkAllButtons() {
        console.log('=== 按钮状态检查 ===');
        
        // D1成员按钮
        const memberBtn = document.querySelector('#add-member-btn');
        console.log(`D1成员按钮: ${memberBtn ? (memberBtn.disabled ? '禁用' : '可用') : '未找到'}`);
        
        // D3措施按钮
        const measureBtn = document.querySelector('#add-measure-btn');
        console.log(`D3措施按钮: ${measureBtn ? (measureBtn.disabled ? '禁用' : '可用') : '未找到'}`);
        
        // D5措施按钮
        const d5Btn = document.querySelector('#add-d5-measure-btn');
        console.log(`D5措施按钮: ${d5Btn ? (d5Btn.disabled ? '禁用' : '可用') : '未找到'}`);
        
        // D6验证按钮
        const d6Btn = document.querySelector('#add-d6-verification-btn');
        console.log(`D6验证按钮: ${d6Btn ? (d6Btn.disabled ? '禁用' : '可用') : '未找到'}`);
        
        // D7预防按钮
        const d7Btn = document.querySelector('#add-d7-prevention-btn');
        console.log(`D7预防按钮: ${d7Btn ? (d7Btn.disabled ? '禁用' : '可用') : '未找到'}`);
        
        // D4原因按钮
        const causeCategories = ['man', 'machine', 'material', 'method', 'environment', 'measurement'];
        causeCategories.forEach(category => {
            const btn = document.querySelector(`#add-${category}-cause-btn`);
            console.log(`D4 ${category}原因按钮: ${btn ? (btn.disabled ? '禁用' : '可用') : '未找到'}`);
        });
        
        console.log('=== 按钮检查完成 ===');
    },
    
    /**
     * 强制更新所有计数器
     */
    forceUpdateCounters() {
        console.log('强制更新所有计数器...');
        
        try {
            if (window.updateMemberCount) window.updateMemberCount();
            if (window.updateMeasureCount) window.updateMeasureCount();
            if (window.updateD5MeasureCount) window.updateD5MeasureCount();
            if (window.updateD6VerificationCount) window.updateD6VerificationCount();
            if (window.updateD7PreventionCount) window.updateD7PreventionCount();
            
            console.log('计数器更新完成');
        } catch (error) {
            console.error('更新计数器时出错:', error);
        }
    },
    
    /**
     * 强制更新所有按钮状态
     */
    forceUpdateButtons() {
        console.log('强制更新所有按钮状态...');
        
        try {
            if (window.checkMemberLimit) window.checkMemberLimit();
            if (window.checkMeasureLimit) window.checkMeasureLimit();
            if (window.checkD5MeasureLimit) window.checkD5MeasureLimit();
            if (window.checkD6VerificationLimit) window.checkD6VerificationLimit();
            if (window.checkD7PreventionLimit) window.checkD7PreventionLimit();
            if (window.checkAllCauseLimits) window.checkAllCauseLimits();
            
            console.log('按钮状态更新完成');
        } catch (error) {
            console.error('更新按钮状态时出错:', error);
        }
    },
    
    /**
     * 完整的状态检查和修复
     */
    fullCheck() {
        console.log('=== 完整状态检查 ===');
        this.checkAllCounters();
        this.checkAllButtons();
        console.log('\n强制更新...');
        this.forceUpdateCounters();
        this.forceUpdateButtons();
        console.log('\n更新后状态:');
        this.checkAllCounters();
        this.checkAllButtons();
        console.log('=== 检查完成 ===');
    }
};

// 在控制台中提示用户如何使用
console.log('调试工具已加载！使用方法：');
console.log('debugCounters.checkAllCounters() - 检查计数器状态');
console.log('debugCounters.checkAllButtons() - 检查按钮状态');
console.log('debugCounters.forceUpdateCounters() - 强制更新计数器');
console.log('debugCounters.forceUpdateButtons() - 强制更新按钮状态');
console.log('debugCounters.fullCheck() - 完整检查和修复');
