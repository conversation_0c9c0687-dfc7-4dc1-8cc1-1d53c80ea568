#!/usr/bin/env python3
"""
简单的更新测试
"""

import requests
import json

BASE_URL = 'http://localhost:5555'

def login_and_get_cookies():
    """登录并获取cookies"""
    data = {
        'username': 'testuser',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/auth/login', json=data)
        if response.json().get('success'):
            return response.cookies
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def test_update():
    cookies = login_and_get_cookies()
    if not cookies:
        print("登录失败")
        return
    
    # 尝试更新版本
    update_data = {
        'form_data': {
            'problem_description': '测试更新',
            'product_name': '测试产品'
        }
    }
    
    try:
        response = requests.put(f'{BASE_URL}/api/conversations/autosave_test_conv/versions/autosave_v1',
                              json=update_data, cookies=cookies)
        
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"成功: {result.get('success')}")
            print(f"消息: {result.get('message')}")
        
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == '__main__':
    test_update()
