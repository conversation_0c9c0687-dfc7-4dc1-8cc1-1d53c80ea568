"""
用户认证和授权模块
提供用户注册、登录、会话管理等功能
"""

from flask import request, jsonify, session, g
from functools import wraps
from database import user_manager, session_manager
from logger_config import logger, log_business_operation, log_error_operation
from typing import <PERSON>ple
import re


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_password(password: str) -> Tuple[bool, str]:
    """验证密码强度"""
    if len(password) < 6:
        return False, "密码长度至少6位"
    
    if len(password) > 50:
        return False, "密码长度不能超过50位"
    
    # 可以添加更多密码复杂度要求
    return True, ""


def validate_username(username: str) -> <PERSON><PERSON>[bool, str]:
    """验证用户名格式"""
    if len(username) < 3:
        return False, "用户名长度至少3位"
    
    if len(username) > 20:
        return False, "用户名长度不能超过20位"
    
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        return False, "用户名只能包含字母、数字和下划线"
    
    return True, ""


def register_user():
    """用户注册"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供注册信息'}), 400
        
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '')
        full_name = data.get('full_name', '').strip()
        department = data.get('department', '').strip()
        
        # 验证必填字段
        if not username or not email or not password:
            return jsonify({'success': False, 'message': '用户名、邮箱和密码为必填项'}), 400
        
        # 验证用户名格式
        valid_username, username_msg = validate_username(username)
        if not valid_username:
            return jsonify({'success': False, 'message': username_msg}), 400
        
        # 验证邮箱格式
        if not validate_email(email):
            return jsonify({'success': False, 'message': '邮箱格式不正确'}), 400
        
        # 验证密码强度
        valid_password, password_msg = validate_password(password)
        if not valid_password:
            return jsonify({'success': False, 'message': password_msg}), 400
        
        # 创建用户
        user_id = user_manager.create_user(
            username=username,
            email=email,
            password=password,
            full_name=full_name or None,
            department=department or None
        )
        
        if user_id:
            log_business_operation('user_registered', {
                'user_id': user_id, 
                'username': username,
                'ip': request.remote_addr
            })
            return jsonify({
                'success': True, 
                'message': '注册成功',
                'user_id': user_id
            }), 201
        else:
            return jsonify({'success': False, 'message': '注册失败'}), 500
            
    except ValueError as e:
        return jsonify({'success': False, 'message': str(e)}), 400
    except Exception as e:
        log_error_operation('user_registration_failed', e, {
            'username': data.get('username') if 'data' in locals() else None,
            'ip': request.remote_addr
        })
        return jsonify({'success': False, 'message': '注册失败，请稍后重试'}), 500


def login_user():
    """用户登录"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'message': '请提供登录信息'}), 400
        
        username = data.get('username', '').strip()
        password = data.get('password', '')
        
        if not username or not password:
            return jsonify({'success': False, 'message': '用户名和密码为必填项'}), 400
        
        # 用户认证
        user = user_manager.authenticate_user(username, password)
        
        if user:
            # 创建会话
            session_id = session_manager.create_session(
                user_id=user['id'],
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent')
            )
            
            # 设置Flask session
            session.permanent = True  # 标记为永久session
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['session_id'] = session_id
            
            log_business_operation('user_logged_in', {
                'user_id': user['id'],
                'username': username,
                'ip': request.remote_addr
            })
            
            return jsonify({
                'success': True,
                'message': '登录成功',
                'user': {
                    'id': user['id'],
                    'username': user['username'],
                    'full_name': user['full_name'],
                    'department': user['department'],
                    'role': user['role']
                },
                'session_id': session_id
            }), 200
        else:
            log_business_operation('login_failed', {
                'username': username,
                'ip': request.remote_addr,
                'reason': 'invalid_credentials'
            })
            return jsonify({'success': False, 'message': '用户名或密码错误'}), 401
            
    except Exception as e:
        log_error_operation('login_error', e, {
            'username': data.get('username') if 'data' in locals() else None,
            'ip': request.remote_addr
        })
        return jsonify({'success': False, 'message': '登录失败，请稍后重试'}), 500


def logout_user():
    """用户登出"""
    try:
        session_id = session.get('session_id')
        user_id = session.get('user_id')
        
        if session_id:
            # 使会话失效
            session_manager.invalidate_session(session_id)
        
        # 清除Flask session
        session.clear()
        
        log_business_operation('user_logged_out', {
            'user_id': user_id,
            'session_id': session_id,
            'ip': request.remote_addr
        })
        
        return jsonify({'success': True, 'message': '登出成功'}), 200
        
    except Exception as e:
        log_error_operation('logout_error', e, {
            'user_id': session.get('user_id'),
            'ip': request.remote_addr
        })
        return jsonify({'success': False, 'message': '登出失败'}), 500


def get_current_user():
    """获取当前用户信息"""
    try:
        user_id = session.get('user_id')
        session_id = session.get('session_id')
        
        if not user_id or not session_id:
            return jsonify({'success': False, 'message': '未登录'}), 401
        
        # 验证会话
        session_data = session_manager.validate_session(session_id)
        if not session_data:
            session.clear()
            return jsonify({'success': False, 'message': '会话已过期，请重新登录'}), 401
        
        # 获取用户信息
        user = user_manager.get_user_by_id(user_id)
        if not user:
            session.clear()
            return jsonify({'success': False, 'message': '用户不存在'}), 401
        
        return jsonify({
            'success': True,
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'full_name': user['full_name'],
                'department': user['department'],
                'role': user['role'],
                'last_login': user['last_login']
            }
        }), 200
        
    except Exception as e:
        log_error_operation('get_current_user_error', e, {
            'user_id': session.get('user_id'),
            'ip': request.remote_addr
        })
        return jsonify({'success': False, 'message': '获取用户信息失败'}), 500


def require_auth(f):
    """认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user_id = session.get('user_id')
        session_id = session.get('session_id')
        
        if not user_id or not session_id:
            return jsonify({'success': False, 'message': '请先登录'}), 401
        
        # 验证会话
        session_data = session_manager.validate_session(session_id)
        if not session_data:
            session.clear()
            return jsonify({'success': False, 'message': '会话已过期，请重新登录'}), 401
        
        # 将用户信息添加到g对象中，供视图函数使用
        g.current_user_id = user_id
        g.current_user = user_manager.get_user_by_id(user_id)
        
        return f(*args, **kwargs)
    
    return decorated_function


def require_admin(f):
    """管理员权限装饰器"""
    @wraps(f)
    @require_auth
    def decorated_function(*args, **kwargs):
        if g.current_user.get('role') != 'admin':
            return jsonify({'success': False, 'message': '需要管理员权限'}), 403
        
        return f(*args, **kwargs)
    
    return decorated_function
