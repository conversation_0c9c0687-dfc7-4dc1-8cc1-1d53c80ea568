/**
 * 清理浏览器存储的工具
 * 用于清除旧的localStorage数据，确保数据完全由后端管理
 */

window.cleanupStorage = {
    /**
     * 清理所有8D系统相关的localStorage数据
     */
    cleanupAll() {
        console.log('开始清理localStorage中的8D系统数据...');
        
        const keysToRemove = [
            '8d_conversations',
            '8d_form_records',
            '8d_current_conversation',
            '8d_active_version',
            '8d_chat_history',
            '8d_form_data',
            '8d_auto_save',
            '8d_user_settings'
        ];
        
        let removedCount = 0;
        
        keysToRemove.forEach(key => {
            if (localStorage.getItem(key) !== null) {
                localStorage.removeItem(key);
                removedCount++;
                console.log(`已清理: ${key}`);
            }
        });
        
        // 清理所有以8d_开头的键
        const allKeys = Object.keys(localStorage);
        allKeys.forEach(key => {
            if (key.startsWith('8d_') && !keysToRemove.includes(key)) {
                localStorage.removeItem(key);
                removedCount++;
                console.log(`已清理额外项: ${key}`);
            }
        });
        
        console.log(`清理完成，共清理了 ${removedCount} 个项目`);
        
        if (removedCount > 0) {
            console.log('建议刷新页面以确保更改生效');
        }
        
        return removedCount;
    },
    
    /**
     * 检查localStorage中是否还有8D系统数据
     */
    checkRemaining() {
        console.log('检查localStorage中剩余的8D系统数据...');
        
        const allKeys = Object.keys(localStorage);
        const remainingKeys = allKeys.filter(key => key.startsWith('8d_'));
        
        if (remainingKeys.length === 0) {
            console.log('✅ localStorage中没有8D系统数据');
        } else {
            console.log(`❌ localStorage中仍有 ${remainingKeys.length} 个8D系统数据项:`);
            remainingKeys.forEach(key => {
                console.log(`  - ${key}: ${localStorage.getItem(key)?.substring(0, 100)}...`);
            });
        }
        
        return remainingKeys;
    },
    
    /**
     * 显示当前localStorage的使用情况
     */
    showUsage() {
        console.log('localStorage使用情况:');
        
        let totalSize = 0;
        let itemCount = 0;
        
        for (let key in localStorage) {
            if (localStorage.hasOwnProperty(key)) {
                const value = localStorage.getItem(key);
                const size = new Blob([value]).size;
                totalSize += size;
                itemCount++;
                
                if (key.startsWith('8d_')) {
                    console.log(`  8D项目 - ${key}: ${(size / 1024).toFixed(2)} KB`);
                }
            }
        }
        
        console.log(`总计: ${itemCount} 个项目, ${(totalSize / 1024).toFixed(2)} KB`);
        
        // 估算localStorage容量
        try {
            const testKey = 'test_capacity';
            let testData = '';
            let capacity = 0;
            
            // 尝试填充localStorage直到达到限制
            while (capacity < 10 * 1024 * 1024) { // 最多测试10MB
                testData += 'a'.repeat(1024); // 每次增加1KB
                try {
                    localStorage.setItem(testKey, testData);
                    capacity = testData.length;
                } catch (e) {
                    break;
                }
            }
            
            localStorage.removeItem(testKey);
            console.log(`估算localStorage容量: ${(capacity / 1024 / 1024).toFixed(2)} MB`);
            
        } catch (e) {
            console.log('无法估算localStorage容量');
        }
    },
    
    /**
     * 完整的清理和验证流程
     */
    fullCleanup() {
        console.log('=== 开始完整清理流程 ===');
        
        // 显示清理前状态
        console.log('\n清理前状态:');
        this.checkRemaining();
        this.showUsage();
        
        // 执行清理
        console.log('\n执行清理:');
        const removedCount = this.cleanupAll();
        
        // 显示清理后状态
        console.log('\n清理后状态:');
        this.checkRemaining();
        this.showUsage();
        
        console.log('\n=== 清理流程完成 ===');
        
        if (removedCount > 0) {
            console.log('⚠️  建议刷新页面以确保所有更改生效');
            console.log('现在所有数据都将从后端API加载，确保用户数据完全隔离');
        }
        
        return removedCount;
    }
};

// 页面加载时自动检查
document.addEventListener('DOMContentLoaded', function() {
    // 延迟检查，避免与其他初始化冲突
    setTimeout(() => {
        const remainingKeys = window.cleanupStorage.checkRemaining();
        if (remainingKeys.length > 0) {
            console.log('发现localStorage中有8D系统数据，建议运行 cleanupStorage.fullCleanup() 进行清理');
        }
    }, 2000);
});

// 在控制台中提示用户如何使用
console.log('localStorage清理工具已加载！使用方法：');
console.log('cleanupStorage.fullCleanup() - 完整清理流程');
console.log('cleanupStorage.cleanupAll() - 清理所有8D数据');
console.log('cleanupStorage.checkRemaining() - 检查剩余数据');
console.log('cleanupStorage.showUsage() - 显示使用情况');
