#!/usr/bin/env python3
"""
测试API接口的脚本
"""

import requests
import json

BASE_URL = 'http://localhost:5555'

def test_register():
    """测试用户注册"""
    print("测试用户注册...")
    
    data = {
        'username': 'testuser',
        'email': '<EMAIL>',
        'password': '123456',
        'full_name': '测试用户'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/auth/register', json=data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json().get('success', False)
    except Exception as e:
        print(f"注册失败: {e}")
        return False

def test_login():
    """测试用户登录"""
    print("\n测试用户登录...")
    
    data = {
        'username': 'testuser',
        'password': '123456'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/auth/login', json=data)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        
        if result.get('success'):
            return response.cookies
        return None
    except Exception as e:
        print(f"登录失败: {e}")
        return None

def test_get_user(cookies):
    """测试获取用户信息"""
    print("\n测试获取用户信息...")
    
    try:
        response = requests.get(f'{BASE_URL}/api/auth/user', cookies=cookies)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json().get('success', False)
    except Exception as e:
        print(f"获取用户信息失败: {e}")
        return False

def test_save_record(cookies):
    """测试保存记录"""
    print("\n测试保存记录...")
    
    data = {
        'record_id': 'test_record_001',
        'title': '测试8D报告',
        'form_data': {
            'problem_description': '这是一个测试问题',
            'product_name': '测试产品A'
        },
        'record_type': 'draft'
    }
    
    try:
        response = requests.post(f'{BASE_URL}/api/records', json=data, cookies=cookies)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.json().get('success', False)
    except Exception as e:
        print(f"保存记录失败: {e}")
        return False

def test_get_records(cookies):
    """测试获取记录"""
    print("\n测试获取记录...")
    
    try:
        response = requests.get(f'{BASE_URL}/api/records', cookies=cookies)
        print(f"状态码: {response.status_code}")
        result = response.json()
        print(f"响应: {result}")
        return result.get('success', False)
    except Exception as e:
        print(f"获取记录失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始API测试...")
    
    # 测试注册
    if test_register():
        print("✓ 注册成功")
    else:
        print("✗ 注册失败")
        return
    
    # 测试登录
    cookies = test_login()
    if cookies:
        print("✓ 登录成功")
    else:
        print("✗ 登录失败")
        return
    
    # 测试获取用户信息
    if test_get_user(cookies):
        print("✓ 获取用户信息成功")
    else:
        print("✗ 获取用户信息失败")
    
    # 测试保存记录
    if test_save_record(cookies):
        print("✓ 保存记录成功")
    else:
        print("✗ 保存记录失败")
    
    # 测试获取记录
    if test_get_records(cookies):
        print("✓ 获取记录成功")
    else:
        print("✗ 获取记录失败")
    
    print("\nAPI测试完成!")

if __name__ == '__main__':
    main()
